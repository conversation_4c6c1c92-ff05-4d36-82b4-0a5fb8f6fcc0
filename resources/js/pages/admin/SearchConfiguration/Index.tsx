import React, { useState, useEffect } from 'react';
import { Head, router, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Search, 
    Settings, 
    AlertTriangle,
    Users,
    Activity,
    RefreshCw,
    Eye,
    EyeOff,
    TrendingUp,
    Clock,
    Target,
    Image
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import MediaPicker from '@/components/MediaPicker';

interface SearchConfig {
    key: string;
    value: any;
    type: 'string' | 'integer' | 'boolean' | 'array' | 'object';
    description: string;
    category: string;
}

interface Statistics {
    guest_searches: {
        total_searches_today: number;
        total_searches_week: number;
        unique_devices_today: number;
        unique_devices_week: number;
        searches_by_hour: Array<{ hour: number; count: number }>;
    };
    current_configs: {
        guest_search_limit: number;
        search_reset_hours: number;
        enable_partial_results: boolean;
        guest_max_visible_results: number;
    };
    impact_metrics: {
        affected_guest_users: number;
        conversion_rate: number;
        average_searches_per_device: number;
    };
}

interface Props {
    configurations: Record<string, Record<string, any>>;
    statistics: Statistics;
    flash?: {
        test_results?: any;
        success?: string;
        error?: string;
    };
}



export default function SearchConfigurationIndex({ configurations, statistics, flash }: Props) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [testResults, setTestResults] = useState<any>(null);



    // Check for flash test results on component load
    useEffect(() => {
        if (flash?.test_results) {
            setTestResults(flash.test_results);
        }
    }, [flash]);

    // Flatten configurations for form - now configs are complete objects
    const flatConfigs: Record<string, SearchConfig> = {};
    Object.entries(configurations).forEach(([category, configs]) => {
        Object.entries(configs).forEach(([key, configObj]) => {
            // configObj is now the complete configuration object from the controller
            flatConfigs[key] = configObj as SearchConfig;
        });
    });

    // Create initial form data with just the values
    const initialFormData: Record<string, any> = {};
    Object.entries(flatConfigs).forEach(([key, config]) => {
        initialFormData[key] = config.value;
    });

    const { data, setData, post, processing, reset } = useForm(initialFormData);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        console.log('Button clicked');
        setIsSubmitting(true);

        // Convert form data to the expected format
        const configurations = Object.entries(data).map(([key, value]) => {
            const config = flatConfigs[key];
            return {
                key: key,
                value: value,
                type: config.type
            };
        });
        console.log('Sending configurations:', configurations);

        // Use router.post for consistent behavior with tests
        router.post('/admin/search-config/update', {
            configurations
        }, {
            onSuccess: () => {
                setTestResults(null);
                console.log('Configuration saved successfully');
            },
            onError: (errors) => {
                console.error('Configuration save failed:', errors);
            },
            onFinish: () => {
                setIsSubmitting(false);
                console.log('Request finished, resetting isSubmitting');
            }
        });
    };

    const handleReset = () => {
        if (confirm('Are you sure you want to reset all configurations to defaults? This action cannot be undone.')) {
            router.post('/admin/search-config/reset');
        }
    };

    const handleTestConfiguration = () => {
        const configurations = Object.entries(data).map(([key, value]) => {
            const config = flatConfigs[key];
            return {
                key: key,
                value: value,
                type: config.type
            };
        });
        
        router.post('/admin/search-config/test', {
            configurations
        }, {
            onSuccess: (page: any) => {
                setTestResults(page.props.flash?.test_results);
            }
        });
    };

    const updateConfigValue = (key: string, value: any) => {
        setData(key, value);
    };

    const renderConfigInput = (config: SearchConfig) => {
        const { key, type, description } = config;
        const currentValue = data[key] ?? config.value;

        switch (type) {
            case 'boolean':
                return (
                    <div className="flex items-center space-x-2">
                        <Switch
                            id={key}
                            checked={currentValue}
                            onCheckedChange={(checked) => updateConfigValue(key, checked)}
                        />
                        <Label htmlFor={key} className="text-sm font-medium">
                            {currentValue ? 'Enabled' : 'Disabled'}
                        </Label>
                    </div>
                );

            case 'integer':
                return (
                    <Input
                        type="number"
                        value={currentValue}
                        onChange={(e) => updateConfigValue(key, parseInt(e.target.value) || 0)}
                        min={key === 'premium_user_daily_limit' ? "-1" : "0"}
                        className="w-full"
                    />
                );

            case 'string':
                if (key === 'blur_intensity') {
                    return (
                        <Select value={currentValue} onValueChange={(value) => updateConfigValue(key, value)}>
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="light">Light</SelectItem>
                                <SelectItem value="medium">Medium</SelectItem>
                                <SelectItem value="heavy">Heavy</SelectItem>
                            </SelectContent>
                        </Select>
                    );
                }
                return (
                    <Input
                        type="text"
                        value={currentValue}
                        onChange={(e) => updateConfigValue(key, e.target.value)}
                        className="w-full"
                    />
                );

            default:
                return (
                    <Input
                        type="text"
                        value={JSON.stringify(currentValue)}
                        onChange={(e) => {
                            try {
                                updateConfigValue(key, JSON.parse(e.target.value));
                            } catch {
                                // Invalid JSON, keep as string
                                updateConfigValue(key, e.target.value);
                            }
                        }}
                        className="w-full"
                    />
                );
        }
    };

    const getCategoryIcon = (category: string) => {
        switch (category) {
            case 'guest_limits':
                return <Users className="h-4 w-4" />;
            case 'user_limits':
                return <Target className="h-4 w-4" />;
            case 'display':
                return <Eye className="h-4 w-4" />;
            case 'partial_results':
                return <EyeOff className="h-4 w-4" />;
            case 'tracking':
                return <Activity className="h-4 w-4" />;
            case 'watermark':
                return <Image className="h-4 w-4" />;
            default:
                return <Settings className="h-4 w-4" />;
        }
    };

    const getCategoryTitle = (category: string) => {
        switch (category) {
            case 'guest_limits':
                return 'Guest User Limits';
            case 'user_limits':
                return 'Authenticated User Limits';
            case 'display':
                return 'Display Settings';
            case 'partial_results':
                return 'Partial Results & Blur Effect';
            case 'tracking':
                return 'Search Tracking';
            case 'watermark':
                return 'Watermark System';
            default:
                return category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        }
    };

    return (
        <AppLayout breadcrumbs={[
            { title: 'Admin', href: '/admin/dashboard' },
            { title: 'Search Configuration', href: '/admin/search-config' }
        ]}>
            <Head title="Search Configuration" />
            
            <div className="container mx-auto px-4 py-6 space-y-6 max-w-7xl">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Search Configuration</h1>
                        <p className="text-muted-foreground">
                            Configure search limits, partial results, and tracking settings
                        </p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button
                            variant="outline"
                            onClick={handleTestConfiguration}
                            disabled={processing}
                        >
                            <Activity className="h-4 w-4 mr-2" />
                            Test Configuration
                        </Button>
                        <Button
                            variant="outline"
                            onClick={handleReset}
                            disabled={processing}
                        >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Reset to Defaults
                        </Button>
                    </div>
                </div>

                <Tabs defaultValue="configuration" className="space-y-6">
                    <TabsList>
                        <TabsTrigger value="configuration">Configuration</TabsTrigger>
                        <TabsTrigger value="watermark">Watermark</TabsTrigger>
                        <TabsTrigger value="statistics">Statistics</TabsTrigger>
                        <TabsTrigger value="impact">Impact Analysis</TabsTrigger>
                    </TabsList>

                    <TabsContent value="configuration" className="space-y-6">
                        {/* Test Results */}
                        {testResults && (
                            <Alert>
                                <AlertTriangle className="h-4 w-4" />
                                <AlertDescription>
                                    <div className="space-y-2">
                                        <p className="font-medium">Configuration Test Results:</p>
                                        {testResults.warnings?.map((warning: string, index: number) => (
                                            <p key={index} className="text-amber-600">⚠️ {warning}</p>
                                        ))}
                                        {testResults.recommendations?.map((rec: string, index: number) => (
                                            <p key={index} className="text-green-600">✅ {rec}</p>
                                        ))}
                                    </div>
                                </AlertDescription>
                            </Alert>
                        )}

                        <form onSubmit={handleSubmit} className="space-y-6">
                            {Object.entries(configurations).map(([category, configs]) => (
                                <Card key={category}>
                                    <CardHeader>
                                        <CardTitle className="flex items-center space-x-2">
                                            {getCategoryIcon(category)}
                                            <span>{getCategoryTitle(category)}</span>
                                        </CardTitle>
                                        <CardDescription>
                                            Configure settings for {getCategoryTitle(category).toLowerCase()}
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        {Object.entries(configs).map(([key, configObj]) => {
                                            // configObj is the complete configuration object from the controller
                                            const config = configObj as SearchConfig;
                                            if (!config) return null;

                                            return (
                                                <div key={key} className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                                                    <div>
                                                        <Label htmlFor={key} className="text-sm font-medium">
                                                            {config.description || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                                        </Label>
                                                        <p className="text-xs text-muted-foreground mt-1">
                                                            {config.description || `Configuration for ${key}`}
                                                        </p>
                                                    </div>
                                                    <div className="md:col-span-2">
                                                        {renderConfigInput(config)}
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </CardContent>
                                </Card>
                            ))}

                            <div className="flex justify-end space-x-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => reset()}
                                    disabled={processing || isSubmitting}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={processing || isSubmitting}
                                >
                                    {isSubmitting ? 'Saving...' : 'Save Configuration'}
                                </Button>
                            </div>
                        </form>
                    </TabsContent>

                    <TabsContent value="watermark" className="space-y-6">
                        <WatermarkConfiguration
                            configurations={configurations.watermark || {}}
                            formData={data}
                            onConfigUpdate={updateConfigValue}
                            processing={processing}
                        />
                    </TabsContent>

                    <TabsContent value="statistics" className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Searches Today</CardTitle>
                                    <Search className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{statistics.guest_searches.total_searches_today}</div>
                                    <p className="text-xs text-muted-foreground">
                                        From {statistics.guest_searches.unique_devices_today} unique devices
                                    </p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Searches This Week</CardTitle>
                                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{statistics.guest_searches.total_searches_week}</div>
                                    <p className="text-xs text-muted-foreground">
                                        From {statistics.guest_searches.unique_devices_week} unique devices
                                    </p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Current Limit</CardTitle>
                                    <Target className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{statistics.current_configs.guest_search_limit}</div>
                                    <p className="text-xs text-muted-foreground">
                                        Resets every {statistics.current_configs.search_reset_hours}h
                                    </p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Visible Results</CardTitle>
                                    <Eye className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{statistics.current_configs.guest_max_visible_results}</div>
                                    <p className="text-xs text-muted-foreground">
                                        {statistics.current_configs.enable_partial_results ? 'Partial results enabled' : 'Partial results disabled'}
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="impact" className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                        <Users className="h-4 w-4" />
                                        <span>Affected Users</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-3xl font-bold">{statistics.impact_metrics.affected_guest_users}</div>
                                    <p className="text-sm text-muted-foreground">Guest users affected by current limits</p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                        <TrendingUp className="h-4 w-4" />
                                        <span>Conversion Rate</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-3xl font-bold">{(statistics.impact_metrics.conversion_rate * 100).toFixed(1)}%</div>
                                    <p className="text-sm text-muted-foreground">Guest to registered user conversion</p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                        <Activity className="h-4 w-4" />
                                        <span>Avg. Searches</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-3xl font-bold">{statistics.impact_metrics.average_searches_per_device.toFixed(1)}</div>
                                    <p className="text-sm text-muted-foreground">Average searches per device</p>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}

// Watermark Configuration Component
interface WatermarkConfigurationProps {
    configurations: Record<string, SearchConfig>;
    formData: Record<string, any>;
    onConfigUpdate: (key: string, value: any) => void;
    processing: boolean;
}

function WatermarkConfiguration({ configurations, formData, onConfigUpdate, processing }: WatermarkConfigurationProps) {
    const [showPreview, setShowPreview] = useState(false);
    const [isMediaPickerOpen, setIsMediaPickerOpen] = useState(false);

    // Get current configuration values - read from form data first, then fall back to configurations
    const enabled = formData.watermark_enabled ?? configurations.watermark_enabled?.value ?? false;
    const logoUrl = formData.watermark_logo_url ?? configurations.watermark_logo_url?.value ?? '';
    const text = formData.watermark_text ?? configurations.watermark_text?.value ?? 'Mobile Parts DB';
    const position = formData.watermark_position ?? configurations.watermark_position?.value ?? 'bottom-right';
    const opacity = formData.watermark_opacity ?? configurations.watermark_opacity?.value ?? 0.3;
    const size = formData.watermark_size ?? configurations.watermark_size?.value ?? 'medium';
    const customWidth = formData.watermark_custom_width ?? configurations.watermark_custom_width?.value ?? 120;
    const customHeight = formData.watermark_custom_height ?? configurations.watermark_custom_height?.value ?? 40;
    const offsetX = formData.watermark_offset_x ?? configurations.watermark_offset_x?.value ?? 16;
    const offsetY = formData.watermark_offset_y ?? configurations.watermark_offset_y?.value ?? 16;
    const showForGuests = formData.watermark_show_for_guests ?? configurations.watermark_show_for_guests?.value ?? true;
    const showForFreeUsers = formData.watermark_show_for_free_users ?? configurations.watermark_show_for_free_users?.value ?? true;
    const showForPremiumUsers = formData.watermark_show_for_premium_users ?? configurations.watermark_show_for_premium_users?.value ?? false;

    return (
        <div className="space-y-6">
            {/* Basic Settings */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <Settings className="h-4 w-4" />
                        <span>Basic Settings</span>
                    </CardTitle>
                    <CardDescription>
                        Configure basic watermark settings and content
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <Label htmlFor="watermark_enabled">Enable Watermark System</Label>
                            <p className="text-sm text-muted-foreground">
                                Show watermarks on search results
                            </p>
                        </div>
                        <Switch
                            id="watermark_enabled"
                            checked={enabled}
                            onCheckedChange={(checked) => onConfigUpdate('watermark_enabled', checked)}
                            disabled={processing}
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="watermark_logo_url">Logo URL</Label>
                            <div className="flex gap-2">
                                <Input
                                    id="watermark_logo_url"
                                    value={logoUrl}
                                    onChange={(e) => onConfigUpdate('watermark_logo_url', e.target.value)}
                                    placeholder="https://example.com/logo.png"
                                    disabled={processing}
                                />
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setIsMediaPickerOpen(true)}
                                    disabled={processing}
                                >
                                    Browse
                                </Button>
                            </div>
                            <p className="text-sm text-muted-foreground">
                                Upload or select a logo image for the watermark
                            </p>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="watermark_text">Fallback Text</Label>
                            <Input
                                id="watermark_text"
                                value={text}
                                onChange={(e) => onConfigUpdate('watermark_text', e.target.value)}
                                placeholder="Mobile Parts DB"
                                disabled={processing}
                            />
                            <p className="text-sm text-muted-foreground">
                                Text shown when no logo is available
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Position and Appearance */}
            <Card>
                <CardHeader>
                    <CardTitle>Position & Appearance</CardTitle>
                    <CardDescription>
                        Configure watermark positioning, size, and opacity
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="watermark_position">Position</Label>
                            <Select
                                value={position}
                                onValueChange={(value) => onConfigUpdate('watermark_position', value)}
                                disabled={processing}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="top-left">Top Left</SelectItem>
                                    <SelectItem value="top-right">Top Right</SelectItem>
                                    <SelectItem value="bottom-left">Bottom Left</SelectItem>
                                    <SelectItem value="bottom-right">Bottom Right</SelectItem>
                                    <SelectItem value="center">Center</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="watermark_size">Size</Label>
                            <Select
                                value={size}
                                onValueChange={(value) => onConfigUpdate('watermark_size', value)}
                                disabled={processing}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="small">Small (80x24)</SelectItem>
                                    <SelectItem value="medium">Medium (120x36)</SelectItem>
                                    <SelectItem value="large">Large (160x48)</SelectItem>
                                    <SelectItem value="custom">Custom</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="watermark_opacity">Opacity</Label>
                            <Input
                                id="watermark_opacity"
                                type="number"
                                min="0.1"
                                max="1.0"
                                step="0.1"
                                value={opacity}
                                onChange={(e) => onConfigUpdate('watermark_opacity', parseFloat(e.target.value))}
                                disabled={processing}
                            />
                        </div>
                    </div>

                    {size === 'custom' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="watermark_custom_width">Custom Width (px)</Label>
                                <Input
                                    id="watermark_custom_width"
                                    type="number"
                                    min="10"
                                    max="500"
                                    value={customWidth}
                                    onChange={(e) => onConfigUpdate('watermark_custom_width', parseInt(e.target.value))}
                                    disabled={processing}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="watermark_custom_height">Custom Height (px)</Label>
                                <Input
                                    id="watermark_custom_height"
                                    type="number"
                                    min="10"
                                    max="200"
                                    value={customHeight}
                                    onChange={(e) => onConfigUpdate('watermark_custom_height', parseInt(e.target.value))}
                                    disabled={processing}
                                />
                            </div>
                        </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="watermark_offset_x">Horizontal Offset (px)</Label>
                            <Input
                                id="watermark_offset_x"
                                type="number"
                                min="0"
                                max="100"
                                value={offsetX}
                                onChange={(e) => onConfigUpdate('watermark_offset_x', parseInt(e.target.value))}
                                disabled={processing}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="watermark_offset_y">Vertical Offset (px)</Label>
                            <Input
                                id="watermark_offset_y"
                                type="number"
                                min="0"
                                max="100"
                                value={offsetY}
                                onChange={(e) => onConfigUpdate('watermark_offset_y', parseInt(e.target.value))}
                                disabled={processing}
                            />
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Display Rules */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <Users className="h-4 w-4" />
                        <span>Display Rules</span>
                    </CardTitle>
                    <CardDescription>
                        Configure which user types should see the watermark
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                                <Label htmlFor="watermark_show_for_guests">Show for Guest Users</Label>
                                <p className="text-sm text-muted-foreground">
                                    Display watermark for non-logged users
                                </p>
                            </div>
                            <Switch
                                id="watermark_show_for_guests"
                                checked={showForGuests}
                                onCheckedChange={(checked) => onConfigUpdate('watermark_show_for_guests', checked)}
                                disabled={processing}
                            />
                        </div>

                        <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                                <Label htmlFor="watermark_show_for_free_users">Show for Free Users</Label>
                                <p className="text-sm text-muted-foreground">
                                    Display watermark for free registered users
                                </p>
                            </div>
                            <Switch
                                id="watermark_show_for_free_users"
                                checked={showForFreeUsers}
                                onCheckedChange={(checked) => onConfigUpdate('watermark_show_for_free_users', checked)}
                                disabled={processing}
                            />
                        </div>

                        <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                                <Label htmlFor="watermark_show_for_premium_users">Show for Premium Users</Label>
                                <p className="text-sm text-muted-foreground">
                                    Display watermark for premium users
                                </p>
                            </div>
                            <Switch
                                id="watermark_show_for_premium_users"
                                checked={showForPremiumUsers}
                                onCheckedChange={(checked) => onConfigUpdate('watermark_show_for_premium_users', checked)}
                                disabled={processing}
                            />
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Preview */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                            <Eye className="h-4 w-4" />
                            <span>Preview</span>
                        </div>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowPreview(!showPreview)}
                        >
                            {showPreview ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                            {showPreview ? 'Hide Preview' : 'Show Preview'}
                        </Button>
                    </CardTitle>
                    <CardDescription>
                        Preview how the watermark will appear on search results
                    </CardDescription>
                </CardHeader>
                {showPreview && (
                    <CardContent>
                        <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {/* Guest Preview */}
                                <div className="space-y-2">
                                    <Label>Guest User View</Label>
                                    <div className="relative border rounded-lg p-4 bg-gray-50 min-h-[120px]">
                                        <div className="text-sm text-gray-600">Sample Part Card</div>
                                        <div className="text-xs text-gray-500 mt-1">iPhone 14 Pro Screen</div>
                                        {enabled && showForGuests && (
                                            <div
                                                className="absolute pointer-events-none"
                                                style={{
                                                    [position.includes('top') ? 'top' : 'bottom']: `${offsetY}px`,
                                                    [position.includes('left') ? 'left' : position.includes('right') ? 'right' : 'left']:
                                                        position === 'center' ? '50%' : `${offsetX}px`,
                                                    transform: position === 'center' ? 'translate(-50%, -50%)' : undefined,
                                                    opacity: opacity,
                                                    fontSize: size === 'small' ? '10px' : size === 'medium' ? '12px' : '14px',
                                                    width: size === 'custom' ? `${customWidth}px` :
                                                           size === 'small' ? '80px' :
                                                           size === 'medium' ? '120px' : '160px',
                                                    height: size === 'custom' ? `${customHeight}px` :
                                                            size === 'small' ? '24px' :
                                                            size === 'medium' ? '36px' : '48px',
                                                }}
                                            >
                                                {logoUrl ? (
                                                    <img
                                                        src={logoUrl}
                                                        alt={text}
                                                        className="w-full h-full object-contain"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full bg-white/80 border border-gray-200 rounded flex items-center justify-center text-gray-600 text-xs">
                                                        {text}
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Free User Preview */}
                                <div className="space-y-2">
                                    <Label>Free User View</Label>
                                    <div className="relative border rounded-lg p-4 bg-gray-50 min-h-[120px]">
                                        <div className="text-sm text-gray-600">Sample Part Card</div>
                                        <div className="text-xs text-gray-500 mt-1">iPhone 14 Pro Screen</div>
                                        {enabled && showForFreeUsers && (
                                            <div
                                                className="absolute pointer-events-none"
                                                style={{
                                                    [position.includes('top') ? 'top' : 'bottom']: `${offsetY}px`,
                                                    [position.includes('left') ? 'left' : position.includes('right') ? 'right' : 'left']:
                                                        position === 'center' ? '50%' : `${offsetX}px`,
                                                    transform: position === 'center' ? 'translate(-50%, -50%)' : undefined,
                                                    opacity: opacity,
                                                    fontSize: size === 'small' ? '10px' : size === 'medium' ? '12px' : '14px',
                                                    width: size === 'custom' ? `${customWidth}px` :
                                                           size === 'small' ? '80px' :
                                                           size === 'medium' ? '120px' : '160px',
                                                    height: size === 'custom' ? `${customHeight}px` :
                                                            size === 'small' ? '24px' :
                                                            size === 'medium' ? '36px' : '48px',
                                                }}
                                            >
                                                {logoUrl ? (
                                                    <img
                                                        src={logoUrl}
                                                        alt={text}
                                                        className="w-full h-full object-contain"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full bg-white/80 border border-gray-200 rounded flex items-center justify-center text-gray-600 text-xs">
                                                        {text}
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Premium User Preview */}
                                <div className="space-y-2">
                                    <Label>Premium User View</Label>
                                    <div className="relative border rounded-lg p-4 bg-gray-50 min-h-[120px]">
                                        <div className="text-sm text-gray-600">Sample Part Card</div>
                                        <div className="text-xs text-gray-500 mt-1">iPhone 14 Pro Screen</div>
                                        {enabled && showForPremiumUsers && (
                                            <div
                                                className="absolute pointer-events-none"
                                                style={{
                                                    [position.includes('top') ? 'top' : 'bottom']: `${offsetY}px`,
                                                    [position.includes('left') ? 'left' : position.includes('right') ? 'right' : 'left']:
                                                        position === 'center' ? '50%' : `${offsetX}px`,
                                                    transform: position === 'center' ? 'translate(-50%, -50%)' : undefined,
                                                    opacity: opacity,
                                                    fontSize: size === 'small' ? '10px' : size === 'medium' ? '12px' : '14px',
                                                    width: size === 'custom' ? `${customWidth}px` :
                                                           size === 'small' ? '80px' :
                                                           size === 'medium' ? '120px' : '160px',
                                                    height: size === 'custom' ? `${customHeight}px` :
                                                            size === 'small' ? '24px' :
                                                            size === 'medium' ? '36px' : '48px',
                                                }}
                                            >
                                                {logoUrl ? (
                                                    <img
                                                        src={logoUrl}
                                                        alt={text}
                                                        className="w-full h-full object-contain"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full bg-white/80 border border-gray-200 rounded flex items-center justify-center text-gray-600 text-xs">
                                                        {text}
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                )}
            </Card>

            {/* MediaPicker for logo selection */}
            <MediaPicker
                isOpen={isMediaPickerOpen}
                onClose={() => setIsMediaPickerOpen(false)}
                onSelect={(media) => {
                    if (media.length > 0) {
                        onConfigUpdate('watermark_logo_url', media[0].url);
                    }
                    setIsMediaPickerOpen(false);
                }}
                multiple={false}
                title="Select Watermark Logo"
                acceptedTypes={['image/*']}
            />
        </div>
    );
}
