<?php

namespace App\Services;

use App\Models\SearchConfiguration;
use App\Models\User;
use Illuminate\Support\Facades\Cache;

class WatermarkService
{
    /**
     * Get watermark configuration for the current user.
     */
    public function getWatermarkConfig(?User $user = null): array
    {
        $cacheKey = 'watermark_config_' . ($user ? $user->id : 'guest');
        
        return Cache::remember($cacheKey, 3600, function () use ($user) {
            $config = [
                'enabled' => SearchConfiguration::get('watermark_enabled', false),
                'logo_url' => SearchConfiguration::get('watermark_logo_url', ''),
                'text' => SearchConfiguration::get('watermark_text', 'Mobile Parts DB'),
                'position' => SearchConfiguration::get('watermark_position', 'bottom-right'),
                'opacity' => SearchConfiguration::get('watermark_opacity', 0.3),
                'size' => SearchConfiguration::get('watermark_size', 'medium'),
                'custom_width' => SearchConfiguration::get('watermark_custom_width', 120),
                'custom_height' => SearchConfiguration::get('watermark_custom_height', 40),
                'offset_x' => SearchConfiguration::get('watermark_offset_x', 16),
                'offset_y' => SearchConfiguration::get('watermark_offset_y', 16),
            ];

            // Determine if watermark should be shown for this user
            $config['show_for_user'] = $this->shouldShowWatermarkForUser($user);

            return $config;
        });
    }

    /**
     * Determine if watermark should be shown for the given user.
     */
    public function shouldShowWatermarkForUser(?User $user = null): bool
    {
        // If watermark is disabled globally, don't show
        if (!SearchConfiguration::get('watermark_enabled', false)) {
            return false;
        }

        // Guest user
        if (!$user) {
            return SearchConfiguration::get('watermark_show_for_guests', true);
        }

        // Premium user
        if ($user->isPremium()) {
            return SearchConfiguration::get('watermark_show_for_premium_users', false);
        }

        // Free registered user
        return SearchConfiguration::get('watermark_show_for_free_users', true);
    }

    /**
     * Get watermark CSS styles based on configuration.
     */
    public function getWatermarkStyles(?User $user = null): array
    {
        $config = $this->getWatermarkConfig($user);

        if (!$config['show_for_user']) {
            return [];
        }

        $styles = [
            'opacity' => $config['opacity'],
            'position' => 'absolute',
            'pointer-events' => 'none',
            'z-index' => '10',
            'user-select' => 'none',
        ];

        // Position styles
        switch ($config['position']) {
            case 'top-left':
                $styles['top'] = $config['offset_y'] . 'px';
                $styles['left'] = $config['offset_x'] . 'px';
                break;
            case 'top-right':
                $styles['top'] = $config['offset_y'] . 'px';
                $styles['right'] = $config['offset_x'] . 'px';
                break;
            case 'bottom-left':
                $styles['bottom'] = $config['offset_y'] . 'px';
                $styles['left'] = $config['offset_x'] . 'px';
                break;
            case 'bottom-right':
                $styles['bottom'] = $config['offset_y'] . 'px';
                $styles['right'] = $config['offset_x'] . 'px';
                break;
            case 'center':
                $styles['top'] = '50%';
                $styles['left'] = '50%';
                $styles['transform'] = 'translate(-50%, -50%)';
                break;
        }

        // Size styles
        if ($config['size'] === 'custom') {
            $styles['width'] = $config['custom_width'] . 'px';
            $styles['height'] = $config['custom_height'] . 'px';
        } else {
            $sizeMap = [
                'small' => ['width' => '80px', 'height' => '24px'],
                'medium' => ['width' => '120px', 'height' => '36px'],
                'large' => ['width' => '160px', 'height' => '48px'],
            ];
            
            if (isset($sizeMap[$config['size']])) {
                $styles['width'] = $sizeMap[$config['size']]['width'];
                $styles['height'] = $sizeMap[$config['size']]['height'];
            }
        }

        return $styles;
    }

    /**
     * Get watermark content (logo URL or text).
     */
    public function getWatermarkContent(?User $user = null): array
    {
        $config = $this->getWatermarkConfig($user);

        if (!$config['show_for_user']) {
            return ['type' => 'none'];
        }

        // Prefer logo over text
        if (!empty($config['logo_url'])) {
            return [
                'type' => 'image',
                'url' => $config['logo_url'],
                'alt' => $config['text'],
            ];
        }

        return [
            'type' => 'text',
            'text' => $config['text'],
        ];
    }

    /**
     * Clear watermark configuration cache.
     */
    public function clearCache(?User $user = null): void
    {
        if ($user) {
            Cache::forget('watermark_config_' . $user->id);
        } else {
            Cache::forget('watermark_config_guest');
        }
    }

    /**
     * Clear all watermark caches.
     */
    public function clearAllCaches(): void
    {
        // Clear guest cache
        Cache::forget('watermark_config_guest');
        
        // Clear user-specific caches (this is a simplified approach)
        // In a production environment, you might want to track active user caches
        $cacheKeys = Cache::getRedis()->keys('*watermark_config_*');
        foreach ($cacheKeys as $key) {
            Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
        }
    }

    /**
     * Validate watermark configuration values.
     */
    public function validateConfig(array $config): array
    {
        $errors = [];

        // Validate opacity
        if (isset($config['watermark_opacity'])) {
            $opacity = (float) $config['watermark_opacity'];
            if ($opacity < 0.1 || $opacity > 1.0) {
                $errors['watermark_opacity'] = 'Opacity must be between 0.1 and 1.0';
            }
        }

        // Validate position
        if (isset($config['watermark_position'])) {
            $validPositions = ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'];
            if (!in_array($config['watermark_position'], $validPositions)) {
                $errors['watermark_position'] = 'Invalid position value';
            }
        }

        // Validate size
        if (isset($config['watermark_size'])) {
            $validSizes = ['small', 'medium', 'large', 'custom'];
            if (!in_array($config['watermark_size'], $validSizes)) {
                $errors['watermark_size'] = 'Invalid size value';
            }
        }

        // Validate custom dimensions
        if (isset($config['watermark_custom_width'])) {
            $width = (int) $config['watermark_custom_width'];
            if ($width < 10 || $width > 500) {
                $errors['watermark_custom_width'] = 'Width must be between 10 and 500 pixels';
            }
        }

        if (isset($config['watermark_custom_height'])) {
            $height = (int) $config['watermark_custom_height'];
            if ($height < 10 || $height > 200) {
                $errors['watermark_custom_height'] = 'Height must be between 10 and 200 pixels';
            }
        }

        // Validate offsets
        if (isset($config['watermark_offset_x'])) {
            $offsetX = (int) $config['watermark_offset_x'];
            if ($offsetX < 0 || $offsetX > 100) {
                $errors['watermark_offset_x'] = 'Horizontal offset must be between 0 and 100 pixels';
            }
        }

        if (isset($config['watermark_offset_y'])) {
            $offsetY = (int) $config['watermark_offset_y'];
            if ($offsetY < 0 || $offsetY > 100) {
                $errors['watermark_offset_y'] = 'Vertical offset must be between 0 and 100 pixels';
            }
        }

        return $errors;
    }
}
